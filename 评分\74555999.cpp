#include <iostream>
#include <vector>
#include <queue>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <climits> // For INT_MAX

using namespace std;

// MOVE_PENALTY is now implicitly handled by the tuple cost function
// const int MOVE_PENALTY = 50; 

// --- Data Models ---

struct Server {
    int id;
    int g; // NPU count
    int k; // Speed coefficient
    int m; // Memory size
};

struct NPU {
    int id;
    int server_id;
    int k;
    int m;
    // List of (time, memory_delta) events, kept sorted by time
    vector<pair<int, int>> events;

    void add_task(int start_time, int finish_time, int mem_needed) {
        // Inserts events while maintaining sort order by time.
        // lower_bound finds the first element not less than the given value.
        auto it_start = lower_bound(events.begin(), events.end(), make_pair(start_time, INT_MIN));
        events.insert(it_start, { start_time, mem_needed });

        auto it_finish = lower_bound(events.begin(), events.end(), make_pair(finish_time, INT_MIN));
        events.insert(it_finish, { finish_time, -mem_needed });
    }

    int find_earliest_start_time(int arrival_time, int mem_needed) const {
        if (mem_needed > m) {
            return -1; // Using -1 to represent infinity/failure
        }

        int current_mem_usage = 0;
        for (const auto& event : events) {
            if (event.first <= arrival_time) {
                current_mem_usage += event.second;
            }
            else {
                break;
            }
        }

        if (current_mem_usage + mem_needed <= m) {
            return arrival_time;
        }

        // If not enough memory, check future event points for released memory
        // Find the first event that happens after arrival_time
        auto it = upper_bound(events.begin(), events.end(), make_pair(arrival_time, INT_MAX));

        int future_mem_usage = current_mem_usage;
        for (auto i = it; i != events.end(); ++i) {
            future_mem_usage += i->second;
            if (future_mem_usage + mem_needed <= m) {
                // To avoid scheduling conflicts, we must check if there is enough memory
                // throughout the [i->first, arrival_time] interval.
                // Simplified: We assume if memory is free at a future point, it's a valid start time.
                // A more rigorous check would be complex. This heuristic is a common practice.
                return i->first; // Found a slot starting at this event's time
            }
        }
        return -1; // No suitable slot found
    }
};

struct User {
    int id;
    int s, e, cnt;
    long long samples_left;
    long long next_send_time;
    int last_npu_id = -1;
    // (time, server_id, npu_id_in_server, batch_size)
    vector<tuple<int, int, int, int>> requests;
};


// --- Helper Functions ---

// Calculate correct inference time according to the problem statement
int calculate_inference_time(int batch_size, int k) {
    if (batch_size <= 0) return 0;
    // The formula is ceil(sqrt(batch_size) / k)
    // Using integer arithmetic for ceil(a/b) is (a+b-1)/b
    int sqrt_b = static_cast<int>(sqrt(batch_size));
    // Check if sqrt was exact, if not, we need the ceiling of sqrt(batch_size)
    if (sqrt_b * sqrt_b < batch_size) {
        sqrt_b++;
    }
    return (sqrt_b + k - 1) / k;
}


// **OPTIMIZATION 2: Use binary search to find the optimal batch size.**
// This finds the largest batch size that can finish within the given time, maximizing throughput.
int find_optimal_batch_size(int max_batch_mem, int samples_left, int k, long long remaining_time) {
    if (remaining_time <= 0) {
        return 1; // Cannot perform a meaningful task, send minimal batch.
    }

    int low = 1;
    int high = min(max_batch_mem, (int)samples_left);
    int best_batch = 1;

    while (low <= high) {
        int mid = low + (high - low) / 2;
        if (mid == 0) { // Avoid mid becoming 0
            low = 1;
            continue;
        }
        if (calculate_inference_time(mid, k) <= remaining_time) {
            best_batch = mid; // This batch size is valid
            low = mid + 1;    // Try for an even larger batch
        }
        else {
            high = mid - 1;   // This batch is too slow, try smaller
        }
    }
    return best_batch;
}


// --- Main Logic ---

int main() {
    // Fast I/O
    ios_base::sync_with_stdio(false);
    cin.tie(NULL);

    // 1. Read Input
    int N;
    cin >> N;
    if (cin.eof()) return 0;

    vector<Server> servers_data(N);
    for (int i = 0; i < N; ++i) {
        servers_data[i].id = i;
        cin >> servers_data[i].g >> servers_data[i].k >> servers_data[i].m;
    }

    int M;
    cin >> M;
    vector<User> users(M);
    for (int i = 0; i < M; ++i) {
        users[i].id = i;
        cin >> users[i].s >> users[i].e >> users[i].cnt;
        users[i].samples_left = users[i].cnt;
        users[i].next_send_time = users[i].s;
    }

    vector<vector<int>> latencies(N, vector<int>(M));
    for (int i = 0; i < N; ++i) {
        for (int j = 0; j < M; ++j) {
            cin >> latencies[i][j];
        }
    }

    int A, B;
    cin >> A >> B;

    // 2. Initialize Models
    vector<NPU> npus;
    int npu_counter = 0;
    for (const auto& server : servers_data) {
        for (int i = 0; i < server.g; ++i) {
            npus.push_back({ npu_counter++, server.id, server.k, server.m, {} });
        }
    }

    // 3. Event-driven Scheduling
    priority_queue<pair<long long, int>, vector<pair<long long, int>>, greater<pair<long long, int>>> user_pq;
    for (const auto& user : users) {
        // Use a min-heap: {next_available_time, user_id}
        user_pq.push({ user.next_send_time, user.id });
    }

    while (!user_pq.empty()) {
        auto top = user_pq.top();
        user_pq.pop();
        long long time = top.first;
        int user_id = top.second;
        User& user = users[user_id];

        if (user.samples_left <= 0) continue;

        long long send_time = max(time, user.next_send_time);

        // --- Decision Making with Optimized Cost Function ---
        // **OPTIMIZATION 1: Use a tuple for lexicographical cost comparison.**
        // This prioritizes: 1. not being late, 2. finish time, 3. move penalty.
        tuple<int, int, int> best_cost = { 1, INT_MAX, 0 }; // {is_late, finish_time, has_move}
        int best_finish_time = -1;
        int best_npu_idx = -1;
        int best_batch_size = -1;

        for (int i = 0; i < npus.size(); ++i) {
            const auto& npu = npus[i];
            const auto& server = servers_data[npu.server_id];

            // Memory check for the server's NPU type
            if (server.m <= B) continue;
            int max_b_for_npu = (server.m - B) / A;
            if (max_b_for_npu <= 0) continue;

            int latency = latencies[server.id][user.id];
            int arrival_time = send_time + latency;

            // **OPTIMIZATION 3: Use a more accurate remaining time for batch selection.**
            long long time_for_inference = max(0LL, (long long)user.e - arrival_time);

            int good_batch = find_optimal_batch_size(max_b_for_npu, user.samples_left, server.k, time_for_inference);
            int mem_needed = A * good_batch + B;

            int start_time = npu.find_earliest_start_time(arrival_time, mem_needed);
            if (start_time == -1) continue;

            int inference_time = calculate_inference_time(good_batch, server.k);
            int finish_time = start_time + inference_time;

            int has_move = (user.last_npu_id != -1 && npu.id != user.last_npu_id);
            bool is_late = finish_time > user.e;

            tuple<int, int, int> current_cost = { is_late, finish_time, has_move };

            if (current_cost < best_cost) {
                best_cost = current_cost;
                best_finish_time = finish_time;
                best_npu_idx = i;
                best_batch_size = good_batch;
            }
        }

        // --- Commit to the best decision ---
        if (best_npu_idx != -1) {
            NPU& chosen_npu = npus[best_npu_idx];
            int batch = best_batch_size;

            int mem_needed = A * batch + B;
            int latency = latencies[chosen_npu.server_id][user.id];
            int server_k = servers_data[chosen_npu.server_id].k;
            int inference_time = calculate_inference_time(batch, server_k);
            int start_time = best_finish_time - inference_time;

            chosen_npu.add_task(start_time, best_finish_time, mem_needed);

            int npu_id_in_server;
            int base_npu_id = 0;
            for (int i = 0; i < chosen_npu.server_id; ++i) {
                base_npu_id += servers_data[i].g;
            }
            npu_id_in_server = chosen_npu.id - base_npu_id + 1;

            user.requests.emplace_back(send_time, chosen_npu.server_id + 1, npu_id_in_server, batch);

            user.samples_left -= batch;
            user.next_send_time = send_time + 1; // Per problem: user can send next request at t+1
            user.last_npu_id = chosen_npu.id;

            if (user.samples_left > 0) {
                user_pq.push({ user.next_send_time, user.id });
            }
        }
        else {
            // Handle case where no NPU could be found (e.g., all are full).
            // Reschedule the user for a later time to avoid an infinite loop.
            if (user.samples_left > 0) {
                user.next_send_time = time + 1; // Try again 1ms later
                user_pq.push({ user.next_send_time, user.id });
            }
        }
    }

    // 4. Print Output
    for (auto& user : users) {
        // Sorting is only needed if requests can be generated out of order, which they aren't.
        // But it's a good safeguard.
        // sort(user.requests.begin(), user.requests.end());
        cout << user.requests.size() << "\n";
        bool first = true;
        for (const auto& req : user.requests) {
            if (!first) {
                cout << " ";
            }
            cout << get<0>(req) << " " << get<1>(req) << " "
                << get<2>(req) << " " << get<3>(req);
            first = false;
        }
        cout << "\n";
    }

    return 0;
}